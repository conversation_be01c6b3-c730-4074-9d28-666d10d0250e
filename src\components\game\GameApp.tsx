import React, { useState, useEffect } from 'react';
import { App, AnimationRoutes, Route, useNavigate } from 'zmp-ui';
import WelcomeScreen from './WelcomeScreen';
import ImageUploadScreen from './ImageUploadScreen';
import SurveyScreen from './SurveyScreen';
import ResultsScreen from './ResultsScreen';

interface GameState {
  sessionId: string;
  currentScreen: number;
  uploadedImages: File[];
  surveyResponses: any[];
  startTime: Date;
  completionTime?: Date;
}

interface SurveyResponse {
  questionId: string;
  answer: string | string[] | number;
}

const GameApp: React.FC = () => {
  const navigate = useNavigate();

  const [gameState, setGameState] = useState<GameState>({
    sessionId: generateSessionId(),
    currentScreen: 1,
    uploadedImages: [],
    surveyResponses: [],
    startTime: new Date()
  });

  // Load saved state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('gameState');
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState);
        setGameState({
          ...parsed,
          startTime: new Date(parsed.startTime),
          completionTime: parsed.completionTime ? new Date(parsed.completionTime) : undefined
        });
      } catch (error) {
        console.error('Error loading saved game state:', error);
      }
    }
  }, []);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('gameState', JSON.stringify(gameState));
  }, [gameState]);

  // Track current screen manually since we're using ZMP navigation
  const updateCurrentScreen = (screen: number) => {
    setGameState(prev => ({ ...prev, currentScreen: screen }));
  };

  function generateSessionId(): string {
    return 'game_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  const handleGameStart = () => {
    setGameState(prev => ({
      ...prev,
      startTime: new Date(),
      currentScreen: 2
    }));
    updateCurrentScreen(2);
  };

  const handleImageUpload = (images: File[]) => {
    setGameState(prev => ({
      ...prev,
      uploadedImages: images
    }));
  };

  const handleSurveyComplete = (responses: SurveyResponse[]) => {
    setGameState(prev => ({
      ...prev,
      surveyResponses: responses,
      completionTime: new Date(),
      currentScreen: 4
    }));
    updateCurrentScreen(4);

    // Send data to backend API
    submitGameData({
      ...gameState,
      surveyResponses: responses,
      completionTime: new Date()
    });
  };

  const handleGameRestart = () => {
    const newState: GameState = {
      sessionId: generateSessionId(),
      currentScreen: 1,
      uploadedImages: [],
      surveyResponses: [],
      startTime: new Date()
    };

    setGameState(newState);
    updateCurrentScreen(1);
    localStorage.removeItem('gameState');
  };

  const submitGameData = async (data: GameState) => {
    try {
      // Create FormData for file uploads
      const formData = new FormData();
      formData.append('sessionId', data.sessionId);
      formData.append('surveyResponses', JSON.stringify(data.surveyResponses));
      formData.append('startTime', data.startTime.toISOString());
      if (data.completionTime) {
        formData.append('completionTime', data.completionTime.toISOString());
      }

      // Add images
      data.uploadedImages.forEach((image, index) => {
        formData.append(`image_${index}`, image);
      });

      const response = await fetch('/api/game/submit', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Failed to submit game data');
      }

      const result = await response.json();
      console.log('Game data submitted successfully:', result);
    } catch (error) {
      console.error('Error submitting game data:', error);
      // Handle error - maybe show a toast notification
    }
  };

  const getProgressPercentage = (): number => {
    return (gameState.currentScreen / 4) * 100;
  };

  const getTimeSpent = (): string => {
    const now = gameState.completionTime || new Date();
    const diffMs = now.getTime() - gameState.startTime.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffSecs = Math.floor((diffMs % 60000) / 1000);
    return `${diffMins}:${diffSecs.toString().padStart(2, '0')}`;
  };

  return (
    <App>
      <div className="game-app">
        {/* Global Progress Bar */}
        <div className="global-progress">
          <div
            className="progress-fill"
            style={{ width: `${getProgressPercentage()}%` }}
          />
        </div>

        {/* Game Timer */}
        <div className="game-timer">
          Time: {getTimeSpent()}
        </div>

        {/* Routes */}
        <AnimationRoutes>
          <Route
            path="/welcome"
            element={
              <WelcomeScreen
                onStart={handleGameStart}
              />
            }
          />
          <Route
            path="/upload"
            element={
              <ImageUploadScreen
                onImageUpload={handleImageUpload}
                uploadedImages={gameState.uploadedImages}
              />
            }
          />
          <Route
            path="/survey"
            element={
              <SurveyScreen
                onSurveyComplete={handleSurveyComplete}
              />
            }
          />
          <Route
            path="/results"
            element={
              <ResultsScreen
                uploadedImages={gameState.uploadedImages}
                surveyResponses={gameState.surveyResponses}
                onRestart={handleGameRestart}
              />
            }
          />
          {/* Default redirect to welcome */}
          <Route
            path="/"
            element={<WelcomeScreen onStart={handleGameStart} />}
          />
        </AnimationRoutes>

        {/* Debug Info (remove in production) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="debug-info">
            <details>
              <summary>Debug Info</summary>
              <pre>{JSON.stringify(gameState, null, 2)}</pre>
            </details>
          </div>
        )}
      </div>
    </App>
  );
};

export default GameApp;
