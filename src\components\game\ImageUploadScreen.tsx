import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, Box, Text, Page, I<PERSON>, Header } from 'zmp-ui';
import { useNavigate } from 'react-router-dom';

interface ImageUploadScreenProps {
  onImageUpload: (images: File[]) => void;
  uploadedImages: File[];
}

const ImageUploadScreen: React.FC<ImageUploadScreenProps> = ({
  onImageUpload,
  uploadedImages
}) => {
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);

  const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxFileSize = 10 * 1024 * 1024; // 10MB
  const maxFiles = 5;

  const validateFile = (file: File): string | null => {
    if (!supportedFormats.includes(file.type)) {
      return 'Unsupported file format. Please use JPG, PNG, GIF, or WebP.';
    }
    if (file.size > maxFileSize) {
      return 'File size too large. Maximum size is 10MB.';
    }
    return null;
  };

  const handleFiles = async (files: FileList) => {
    setUploading(true);
    const validFiles: File[] = [];
    const errors: string[] = [];

    Array.from(files).forEach(file => {
      const error = validateFile(file);
      if (error) {
        errors.push(`${file.name}: ${error}`);
      } else if (validFiles.length + uploadedImages.length < maxFiles) {
        validFiles.push(file);
      } else {
        errors.push(`Maximum ${maxFiles} files allowed`);
      }
    });

    if (errors.length > 0) {
      // Show error toast or modal
      console.error('Upload errors:', errors);
    }

    if (validFiles.length > 0) {
      onImageUpload([...uploadedImages, ...validFiles]);
    }

    setUploading(false);
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const removeImage = (index: number) => {
    const newImages = uploadedImages.filter((_, i) => i !== index);
    onImageUpload(newImages);
  };

  const handleNext = () => {
    if (uploadedImages.length > 0) {
      navigate('/game/survey');
    }
  };

  const handleBack = () => {
    navigate('/game/welcome');
  };

  return (
    <Page className="image-upload-screen">
      {/* Header */}
      <Header
        title="Tải lên hình ảnh"
        showBackIcon
        onBackClick={handleBack}
      />

      <Box className="upload-container">
        {/* Progress Indicator */}
        <Box className="progress-indicator">
          <div className="progress-step completed">1</div>
          <div className="progress-step active">2</div>
          <div className="progress-step">3</div>
          <div className="progress-step">4</div>
        </Box>

        {/* Upload Area */}
        <Box
          className={`upload-area ${dragActive ? 'drag-active' : ''}`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <div className="upload-icon">📸</div>
          <Text.Title>Drop your images here</Text.Title>
          <Text>or click to browse</Text>

          <Button
            variant="primary"
            onClick={handleFileSelect}
            disabled={uploading}
          >
            {uploading ? 'Uploading...' : 'Choose Files'}
          </Button>

          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={supportedFormats.join(',')}
            onChange={(e) => e.target.files && handleFiles(e.target.files)}
            style={{ display: 'none' }}
          />
        </Box>

        {/* File Info */}
        <Box className="file-info">
          <Text>Supported formats: JPG, PNG, GIF, WebP</Text>
          <Text>Maximum file size: 10MB</Text>
          <Text>Maximum files: {maxFiles}</Text>
        </Box>

        {/* Uploaded Images Preview */}
        {uploadedImages.length > 0 && (
          <Box className="uploaded-images">
            <Text.Title>Uploaded Images ({uploadedImages.length})</Text.Title>
            <Box className="image-grid">
              {uploadedImages.map((file, index) => (
                <Box key={index} className="image-preview">
                  <img
                    src={URL.createObjectURL(file)}
                    alt={`Upload ${index + 1}`}
                    className="preview-image"
                  />
                  <Button
                    variant="tertiary"
                    size="small"
                    onClick={() => removeImage(index)}
                    className="remove-button"
                  >
                    <Icon icon="zi-close" />
                  </Button>
                  <Text className="file-name">{file.name}</Text>
                </Box>
              ))}
            </Box>
          </Box>
        )}
      </Box>

      {/* Action Zone */}
      <Box className="action-zone">
        <Box className="navigation-buttons">
          <Button
            variant="secondary"
            onClick={handleBack}
            className="back-button"
          >
            Quay lại
          </Button>

          <Button
            variant="primary"
            onClick={handleNext}
            disabled={uploadedImages.length === 0}
            className="continue-button"
          >
            Tiếp tục ({uploadedImages.length} ảnh)
          </Button>
        </Box>
      </Box>
    </Page>
  );
};

export default ImageUploadScreen;
