import React, { useState, useEffect } from 'react';
import { Button, Box, Text, Page, Icon } from 'zmp-ui';
import { useNavigate } from 'react-router-dom';

interface GameResults {
  score: number;
  category: string;
  insights: string[];
  videoUrl: string;
  shareableImage?: string;
}

interface ResultsScreenProps {
  uploadedImages: File[];
  surveyResponses: any[];
  onRestart: () => void;
}

const ResultsScreen: React.FC<ResultsScreenProps> = ({ 
  uploadedImages, 
  surveyResponses, 
  onRestart 
}) => {
  const navigate = useNavigate();
  const [results, setResults] = useState<GameResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [videoPlaying, setVideoPlaying] = useState(false);

  useEffect(() => {
    // Simulate processing results
    const processResults = async () => {
      setLoading(true);
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Generate mock results based on inputs
      const mockResults: GameResults = {
        score: Math.floor(Math.random() * 100) + 1,
        category: getResultCategory(),
        insights: generateInsights(),
        videoUrl: '/static/reward-video.mp4',
        shareableImage: generateShareableImage()
      };
      
      setResults(mockResults);
      setLoading(false);
    };

    processResults();
  }, [uploadedImages, surveyResponses]);

  const getResultCategory = (): string => {
    const categories = [
      'Creative Explorer',
      'Visual Storyteller', 
      'Artistic Innovator',
      'Digital Pioneer',
      'Aesthetic Curator'
    ];
    return categories[Math.floor(Math.random() * categories.length)];
  };

  const generateInsights = (): string[] => {
    const insights = [
      'Your images show a strong sense of composition and color balance.',
      'You have a unique perspective that sets your work apart.',
      'Your survey responses indicate a creative and analytical mindset.',
      'You demonstrate excellent attention to visual details.',
      'Your style suggests you would excel in digital media creation.'
    ];
    
    // Return 3 random insights
    return insights.sort(() => 0.5 - Math.random()).slice(0, 3);
  };

  const generateShareableImage = (): string => {
    // In a real app, this would generate a custom image with results
    return '/static/shareable-result.png';
  };

  const handleVideoPlay = () => {
    setVideoPlaying(true);
  };

  const handleShare = async () => {
    if (navigator.share && results) {
      try {
        await navigator.share({
          title: 'My Game Results',
          text: `I scored ${results.score} points and got "${results.category}"!`,
          url: window.location.href
        });
      } catch (error) {
        console.log('Error sharing:', error);
        // Fallback to copy to clipboard
        copyToClipboard();
      }
    } else {
      copyToClipboard();
    }
  };

  const copyToClipboard = () => {
    if (results) {
      const shareText = `I scored ${results.score} points and got "${results.category}"! Check out this amazing game!`;
      navigator.clipboard.writeText(shareText);
      // Show toast notification
    }
  };

  const handleRestart = () => {
    onRestart();
    navigate('/game/welcome');
  };

  const handleBack = () => {
    navigate('/game/survey');
  };

  if (loading) {
    return (
      <Page className="results-screen loading">
        <Box className="loading-container">
          <div className="loading-spinner"></div>
          <Text.Title>Processing Your Results...</Text.Title>
          <Text>Analyzing your images and survey responses</Text>
          <Box className="loading-steps">
            <div className="loading-step">✓ Images processed</div>
            <div className="loading-step">✓ Survey analyzed</div>
            <div className="loading-step active">⏳ Generating insights</div>
          </Box>
        </Box>
      </Page>
    );
  }

  if (!results) {
    return (
      <Page className="results-screen error">
        <Box className="error-container">
          <Text.Title>Something went wrong</Text.Title>
          <Text>We couldn't process your results. Please try again.</Text>
          <Button variant="primary" onClick={handleRestart}>
            Start Over
          </Button>
        </Box>
      </Page>
    );
  }

  return (
    <Page className="results-screen">
      <Box className="results-container">
        {/* Header */}
        <Box className="screen-header">
          <Button variant="tertiary" onClick={handleBack}>
            <Icon icon="zi-arrow-left" />
          </Button>
          <Text.Title>Your Results</Text.Title>
        </Box>

        {/* Progress Indicator */}
        <Box className="progress-indicator">
          <div className="progress-step completed">1</div>
          <div className="progress-step completed">2</div>
          <div className="progress-step completed">3</div>
          <div className="progress-step active">4</div>
        </Box>

        {/* Results Summary */}
        <Box className="results-summary">
          <Box className="score-display">
            <Text className="score-label">Your Score</Text>
            <Text className="score-value">{results.score}</Text>
            <Text className="score-max">/ 100</Text>
          </Box>
          
          <Box className="category-display">
            <Text.Title className="category-title">
              You are a {results.category}!
            </Text.Title>
          </Box>
        </Box>

        {/* Insights */}
        <Box className="insights-section">
          <Text.Title>Your Insights</Text.Title>
          <Box className="insights-list">
            {results.insights.map((insight, index) => (
              <Box key={index} className="insight-item">
                <div className="insight-icon">💡</div>
                <Text className="insight-text">{insight}</Text>
              </Box>
            ))}
          </Box>
        </Box>

        {/* Image Gallery */}
        <Box className="image-gallery">
          <Text.Title>Your Uploaded Images</Text.Title>
          <Box className="gallery-grid">
            {uploadedImages.slice(0, 4).map((image, index) => (
              <Box key={index} className="gallery-item">
                <img 
                  src={URL.createObjectURL(image)} 
                  alt={`Upload ${index + 1}`}
                  className="gallery-image"
                />
              </Box>
            ))}
          </Box>
        </Box>

        {/* Reward Video */}
        <Box className="video-section">
          <Text.Title>Your Reward Video</Text.Title>
          <Box className="video-container">
            {!videoPlaying ? (
              <Box className="video-placeholder" onClick={handleVideoPlay}>
                <div className="play-button">▶️</div>
                <Text>Click to watch your reward video</Text>
              </Box>
            ) : (
              <video 
                controls 
                autoPlay 
                className="reward-video"
                poster="/static/video-poster.jpg"
              >
                <source src={results.videoUrl} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            )}
          </Box>
        </Box>

        {/* Actions */}
        <Box className="action-buttons">
          <Button 
            variant="secondary" 
            onClick={handleShare}
            className="share-button"
          >
            <Icon icon="zi-share" />
            Share Results
          </Button>
          
          <Button 
            variant="primary" 
            onClick={handleRestart}
            className="restart-button"
          >
            Play Again
          </Button>
        </Box>

        {/* Statistics */}
        <Box className="statistics">
          <Text.Title>Game Statistics</Text.Title>
          <Box className="stats-grid">
            <Box className="stat-item">
              <Text className="stat-value">{uploadedImages.length}</Text>
              <Text className="stat-label">Images Uploaded</Text>
            </Box>
            <Box className="stat-item">
              <Text className="stat-value">{surveyResponses.length}</Text>
              <Text className="stat-label">Questions Answered</Text>
            </Box>
            <Box className="stat-item">
              <Text className="stat-value">100%</Text>
              <Text className="stat-label">Completion Rate</Text>
            </Box>
          </Box>
        </Box>
      </Box>
    </Page>
  );
};

export default ResultsScreen;
