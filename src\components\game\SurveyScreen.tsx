import React, { useState } from 'react';
import { Button, Box, Text, Page, Icon, Input, Radio, Checkbox, Slider, Header } from 'zmp-ui';
import { useNavigate } from 'react-router-dom';

interface Question {
  id: string;
  type: 'multiple-choice' | 'text' | 'rating' | 'yes-no' | 'checkbox' | 'slider';
  question: string;
  options?: string[];
  required: boolean;
  min?: number;
  max?: number;
}

interface SurveyResponse {
  questionId: string;
  answer: string | string[] | number;
}

interface SurveyScreenProps {
  onSurveyComplete: (responses: SurveyResponse[]) => void;
}

const SurveyScreen: React.FC<SurveyScreenProps> = ({ onSurveyComplete }) => {
  const navigate = useNavigate();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [responses, setResponses] = useState<SurveyResponse[]>([]);
  const [currentAnswer, setCurrentAnswer] = useState<string | string[] | number>('');

  // Sample survey questions - in real app, these would come from API
  const questions: Question[] = [
    {
      id: 'q1',
      type: 'multiple-choice',
      question: '<PERSON><PERSON><PERSON> đích chính của bạn khi chơi game này là gì?',
      options: ['Giải trí', 'Học hỏi', 'Tương tác xã hội', 'Cạnh tranh'],
      required: true
    },
    {
      id: 'q2',
      type: 'rating',
      question: 'Bạn đánh giá kỹ năng chụp ảnh của mình như thế nào?',
      min: 1,
      max: 5,
      required: true
    },
    {
      id: 'q3',
      type: 'checkbox',
      question: 'Bạn sử dụng những nền tảng mạng xã hội nào? (Chọn tất cả)',
      options: ['Facebook', 'Instagram', 'Twitter', 'TikTok', 'LinkedIn', 'YouTube'],
      required: false
    },
    {
      id: 'q4',
      type: 'text',
      question: 'Bạn hy vọng đạt được điều gì từ trải nghiệm này?',
      required: true
    },
    {
      id: 'q5',
      type: 'yes-no',
      question: 'Bạn có muốn giới thiệu game này cho bạn bè không?',
      required: true
    },
    {
      id: 'q6',
      type: 'slider',
      question: 'Bạn dành bao nhiều thời gian mỗi ngày cho game mobile? (giờ)',
      min: 0,
      max: 8,
      required: true
    }
  ];

  const currentQuestion = questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === questions.length - 1;

  const handleAnswerChange = (answer: string | string[] | number) => {
    setCurrentAnswer(answer);
  };

  const handleNext = () => {
    // Save current answer
    const newResponse: SurveyResponse = {
      questionId: currentQuestion.id,
      answer: currentAnswer
    };

    const updatedResponses = responses.filter(r => r.questionId !== currentQuestion.id);
    updatedResponses.push(newResponse);
    setResponses(updatedResponses);

    if (isLastQuestion) {
      // Complete survey
      onSurveyComplete(updatedResponses);
      navigate('/game/results');
    } else {
      // Move to next question
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      // Load existing answer if available
      const existingResponse = updatedResponses.find(r => r.questionId === questions[currentQuestionIndex + 1].id);
      setCurrentAnswer(existingResponse?.answer || '');
    }
  };

  const handleBack = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      // Load previous answer
      const existingResponse = responses.find(r => r.questionId === questions[currentQuestionIndex - 1].id);
      setCurrentAnswer(existingResponse?.answer || '');
    } else {
      navigate('/game/upload');
    }
  };

  const isAnswerValid = () => {
    if (!currentQuestion.required) return true;

    switch (currentQuestion.type) {
      case 'text':
        return typeof currentAnswer === 'string' && currentAnswer.trim().length > 0;
      case 'checkbox':
        return Array.isArray(currentAnswer) && currentAnswer.length > 0;
      case 'slider':
      case 'rating':
        return typeof currentAnswer === 'number' && currentAnswer > 0;
      default:
        return currentAnswer !== '';
    }
  };

  const renderQuestion = () => {
    switch (currentQuestion.type) {
      case 'multiple-choice':
        return (
          <Radio.Group
            value={currentAnswer as string}
            onChange={handleAnswerChange}
          >
            {currentQuestion.options?.map((option, index) => (
              <Radio key={index} value={option} label={option} />
            ))}
          </Radio.Group>
        );

      case 'checkbox':
        return (
          <Checkbox.Group
            value={currentAnswer as string[]}
            onChange={handleAnswerChange}
          >
            {currentQuestion.options?.map((option, index) => (
              <Checkbox key={index} value={option} label={option} />
            ))}
          </Checkbox.Group>
        );

      case 'text':
        return (
          <Input.TextArea
            value={currentAnswer as string}
            onChange={(e) => handleAnswerChange(e.target.value)}
            placeholder="Type your answer here..."
            rows={4}
          />
        );

      case 'rating':
        return (
          <Box className="rating-container">
            <Box className="rating-buttons">
              {Array.from({ length: currentQuestion.max || 5 }, (_, i) => i + 1).map(rating => (
                <Button
                  key={rating}
                  variant={currentAnswer === rating ? 'primary' : 'secondary'}
                  onClick={() => handleAnswerChange(rating)}
                  className="rating-button"
                >
                  {rating}
                </Button>
              ))}
            </Box>
            <Box className="rating-labels">
              <Text>Poor</Text>
              <Text>Excellent</Text>
            </Box>
          </Box>
        );

      case 'yes-no':
        return (
          <Radio.Group
            value={currentAnswer as string}
            onChange={handleAnswerChange}
          >
            <Radio value="yes" label="Yes" />
            <Radio value="no" label="No" />
          </Radio.Group>
        );

      case 'slider':
        return (
          <Box className="slider-container">
            <Slider
              value={currentAnswer as number}
              onChange={handleAnswerChange}
              min={currentQuestion.min || 0}
              max={currentQuestion.max || 10}
              step={1}
            />
            <Box className="slider-labels">
              <Text>{currentQuestion.min || 0}</Text>
              <Text className="slider-value">{currentAnswer}</Text>
              <Text>{currentQuestion.max || 10}</Text>
            </Box>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Page className="survey-screen">
      {/* Header */}
      <Header
        title="Khảo sát"
        showBackIcon
        onBackClick={handleBack}
      />

      <Box className="survey-container">
        {/* Progress Indicator */}
        <Box className="progress-indicator">
          <div className="progress-step completed">1</div>
          <div className="progress-step completed">2</div>
          <div className="progress-step active">3</div>
          <div className="progress-step">4</div>
        </Box>

        {/* Question Progress */}
        <Box className="question-progress">
          <Text>Câu hỏi {currentQuestionIndex + 1} / {questions.length}</Text>
          <Box className="progress-bar">
            <div
              className="progress-fill"
              style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
            />
          </Box>
        </Box>

        {/* Question */}
        <Box className="question-container">
          <Text.Title className="question-text">
            {currentQuestion.question}
            {currentQuestion.required && <span className="required">*</span>}
          </Text.Title>

          <Box className="answer-container">
            {renderQuestion()}
          </Box>
        </Box>
      </Box>

      {/* Action Zone */}
      <Box className="action-zone">
        <Box className="navigation-buttons">
          <Button
            variant="secondary"
            onClick={handleBack}
            className="back-button"
          >
            {currentQuestionIndex > 0 ? 'Trước' : 'Quay lại'}
          </Button>

          <Button
            variant="primary"
            onClick={handleNext}
            disabled={!isAnswerValid()}
            className="next-button"
          >
            {isLastQuestion ? 'Hoàn thành' : 'Tiếp theo'}
          </Button>
        </Box>
      </Box>
    </Page>
  );
};

export default SurveyScreen;
