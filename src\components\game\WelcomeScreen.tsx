import React from 'react';
import { <PERSON><PERSON>, Box, Text, Page } from 'zmp-ui';
import { useNavigate } from 'react-router-dom';

interface WelcomeScreenProps {
  onStart: () => void;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onStart }) => {
  const navigate = useNavigate();

  const handleStartGame = () => {
    onStart();
    navigate('/game/upload');
  };

  return (
    <Page className="welcome-screen">
      <Box className="welcome-container">
        {/* Hero Section */}
        <Box className="hero-section">
          <Box className="game-logo">
            <img 
              src="/static/game-logo.png" 
              alt="Game Logo" 
              className="logo-image"
            />
          </Box>
          
          <Text.Title className="game-title">
            Interactive Game Experience
          </Text.Title>
          
          <Text className="game-subtitle">
            Upload your images, answer questions, and discover amazing results!
          </Text>
        </Box>

        {/* Features Section */}
        <Box className="features-section">
          <Box className="feature-item">
            <div className="feature-icon">📸</div>
            <Text className="feature-text">Upload Your Images</Text>
          </Box>
          
          <Box className="feature-item">
            <div className="feature-icon">📝</div>
            <Text className="feature-text">Answer Survey Questions</Text>
          </Box>
          
          <Box className="feature-item">
            <div className="feature-icon">🎬</div>
            <Text className="feature-text">Watch Reward Videos</Text>
          </Box>
          
          <Box className="feature-item">
            <div className="feature-icon">🏆</div>
            <Text className="feature-text">Get Your Results</Text>
          </Box>
        </Box>

        {/* Call to Action */}
        <Box className="cta-section">
          <Button 
            variant="primary" 
            size="large"
            fullWidth
            onClick={handleStartGame}
            className="start-button"
          >
            Start Your Journey
          </Button>
          
          <Text className="disclaimer">
            This game takes approximately 5-10 minutes to complete
          </Text>
        </Box>

        {/* Progress Indicator */}
        <Box className="progress-indicator">
          <div className="progress-step active">1</div>
          <div className="progress-step">2</div>
          <div className="progress-step">3</div>
          <div className="progress-step">4</div>
        </Box>
      </Box>
    </Page>
  );
};

export default WelcomeScreen;
