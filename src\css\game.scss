// Game Application Styles
// Mobile-first responsive design with CSS Grid and Flexbox

// CSS Variables for theming
:root {
  // Colors
  --primary-color: #007bff;
  --primary-hover: #0056b3;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  
  // Neutral colors
  --white: #ffffff;
  --light-gray: #f8f9fa;
  --gray: #6c757d;
  --dark-gray: #343a40;
  --black: #000000;
  
  // Spacing
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-xxl: 3rem;
  
  // Typography
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-xxl: 1.5rem;
  --font-size-xxxl: 2rem;
  
  // Border radius
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 1rem;
  --border-radius-xl: 1.5rem;
  
  // Shadows
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  
  // Transitions
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

// Global Game App Styles
.game-app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: var(--font-family);
  position: relative;
  
  .global-progress {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: rgba(255, 255, 255, 0.2);
    z-index: 1000;
    
    .progress-fill {
      height: 100%;
      background: var(--success-color);
      transition: width var(--transition-normal);
    }
  }
  
  .game-timer {
    position: fixed;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: rgba(0, 0, 0, 0.7);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    z-index: 999;
  }
}

// Common Screen Styles
.screen-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--white);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.progress-indicator {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  
  .progress-step {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--white);
    background: var(--gray);
    transition: all var(--transition-normal);
    
    &.active {
      background: var(--primary-color);
      transform: scale(1.1);
    }
    
    &.completed {
      background: var(--success-color);
    }
  }
}

.navigation-buttons {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  
  button {
    flex: 1;
    min-height: 48px;
  }
}

// Welcome Screen Styles
.welcome-screen {
  .welcome-container {
    padding: var(--spacing-lg);
    max-width: 600px;
    margin: 0 auto;
  }
  
  .hero-section {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
    
    .game-logo {
      margin-bottom: var(--spacing-lg);
      
      .logo-image {
        width: 120px;
        height: 120px;
        border-radius: var(--border-radius-xl);
        box-shadow: var(--shadow-lg);
      }
    }
    
    .game-title {
      font-size: var(--font-size-xxxl);
      font-weight: bold;
      color: var(--white);
      margin-bottom: var(--spacing-md);
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .game-subtitle {
      font-size: var(--font-size-lg);
      color: rgba(255, 255, 255, 0.9);
      line-height: 1.5;
    }
  }
  
  .features-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xxl);
    
    .feature-item {
      background: rgba(255, 255, 255, 0.1);
      padding: var(--spacing-lg);
      border-radius: var(--border-radius-lg);
      text-align: center;
      backdrop-filter: blur(10px);
      
      .feature-icon {
        font-size: var(--font-size-xxxl);
        margin-bottom: var(--spacing-sm);
      }
      
      .feature-text {
        color: var(--white);
        font-weight: 500;
      }
    }
  }
  
  .cta-section {
    text-align: center;
    
    .start-button {
      margin-bottom: var(--spacing-md);
      font-size: var(--font-size-lg);
      padding: var(--spacing-lg) var(--spacing-xl);
    }
    
    .disclaimer {
      color: rgba(255, 255, 255, 0.8);
      font-size: var(--font-size-sm);
    }
  }
}

// Image Upload Screen Styles
.image-upload-screen {
  .upload-container {
    padding: var(--spacing-lg);
    max-width: 800px;
    margin: 0 auto;
  }
  
  .upload-area {
    border: 3px dashed var(--gray);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xxl);
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    margin-bottom: var(--spacing-lg);
    transition: all var(--transition-normal);
    cursor: pointer;
    
    &:hover,
    &.drag-active {
      border-color: var(--primary-color);
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }
    
    .upload-icon {
      font-size: 4rem;
      margin-bottom: var(--spacing-md);
    }
  }
  
  .file-info {
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
    
    p {
      margin: var(--spacing-xs) 0;
      color: rgba(255, 255, 255, 0.9);
      font-size: var(--font-size-sm);
    }
  }
  
  .uploaded-images {
    .image-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: var(--spacing-md);
      
      .image-preview {
        position: relative;
        background: var(--white);
        border-radius: var(--border-radius-md);
        overflow: hidden;
        box-shadow: var(--shadow-md);
        
        .preview-image {
          width: 100%;
          height: 120px;
          object-fit: cover;
        }
        
        .remove-button {
          position: absolute;
          top: var(--spacing-xs);
          right: var(--spacing-xs);
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: var(--danger-color);
          color: var(--white);
          border: none;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        
        .file-name {
          padding: var(--spacing-xs);
          font-size: var(--font-size-xs);
          color: var(--dark-gray);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

// Survey Screen Styles
.survey-screen {
  .survey-container {
    padding: var(--spacing-lg);
    max-width: 700px;
    margin: 0 auto;
  }
  
  .question-progress {
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-lg);
    
    .progress-bar {
      height: 8px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius-sm);
      margin-top: var(--spacing-sm);
      overflow: hidden;
      
      .progress-fill {
        height: 100%;
        background: var(--success-color);
        transition: width var(--transition-normal);
      }
    }
  }
  
  .question-container {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-lg);
    
    .question-text {
      margin-bottom: var(--spacing-lg);
      
      .required {
        color: var(--danger-color);
        margin-left: var(--spacing-xs);
      }
    }
    
    .rating-container {
      .rating-buttons {
        display: flex;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
        
        .rating-button {
          flex: 1;
          aspect-ratio: 1;
          border-radius: 50%;
        }
      }
      
      .rating-labels {
        display: flex;
        justify-content: space-between;
        font-size: var(--font-size-sm);
        color: var(--gray);
      }
    }
    
    .slider-container {
      .slider-labels {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: var(--spacing-md);
        
        .slider-value {
          background: var(--primary-color);
          color: var(--white);
          padding: var(--spacing-xs) var(--spacing-sm);
          border-radius: var(--border-radius-sm);
          font-weight: bold;
        }
      }
    }
  }
}

// Results Screen Styles
.results-screen {
  .results-container {
    padding: var(--spacing-lg);
    max-width: 800px;
    margin: 0 auto;
  }
  
  .results-summary {
    background: linear-gradient(135deg, var(--success-color), #20c997);
    color: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    text-align: center;
    margin-bottom: var(--spacing-lg);
    
    .score-display {
      display: flex;
      align-items: baseline;
      justify-content: center;
      gap: var(--spacing-sm);
      margin-bottom: var(--spacing-md);
      
      .score-value {
        font-size: 4rem;
        font-weight: bold;
      }
      
      .score-max {
        font-size: var(--font-size-xl);
        opacity: 0.8;
      }
    }
  }
  
  .insights-section {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    
    .insights-list {
      .insight-item {
        display: flex;
        gap: var(--spacing-md);
        padding: var(--spacing-md) 0;
        border-bottom: 1px solid var(--light-gray);
        
        &:last-child {
          border-bottom: none;
        }
        
        .insight-icon {
          font-size: var(--font-size-lg);
        }
        
        .insight-text {
          flex: 1;
          line-height: 1.5;
        }
      }
    }
  }
  
  .image-gallery {
    margin-bottom: var(--spacing-lg);
    
    .gallery-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: var(--spacing-md);
      
      .gallery-item {
        border-radius: var(--border-radius-md);
        overflow: hidden;
        box-shadow: var(--shadow-md);
        
        .gallery-image {
          width: 100%;
          height: 120px;
          object-fit: cover;
        }
      }
    }
  }
  
  .video-section {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    
    .video-container {
      .video-placeholder {
        background: var(--dark-gray);
        color: var(--white);
        padding: var(--spacing-xxl);
        text-align: center;
        border-radius: var(--border-radius-md);
        cursor: pointer;
        transition: all var(--transition-normal);
        
        &:hover {
          background: lighten(var(--dark-gray), 10%);
        }
        
        .play-button {
          font-size: 3rem;
          margin-bottom: var(--spacing-md);
        }
      }
      
      .reward-video {
        width: 100%;
        border-radius: var(--border-radius-md);
      }
    }
  }
  
  .action-buttons {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    
    button {
      flex: 1;
      min-height: 48px;
    }
  }
  
  .statistics {
    background: rgba(255, 255, 255, 0.1);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: var(--spacing-lg);
      
      .stat-item {
        text-align: center;
        
        .stat-value {
          font-size: var(--font-size-xxl);
          font-weight: bold;
          color: var(--white);
          display: block;
        }
        
        .stat-label {
          color: rgba(255, 255, 255, 0.8);
          font-size: var(--font-size-sm);
        }
      }
    }
  }
}

// Loading Screen Styles
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: var(--white);
  
  .loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-lg);
  }
  
  .loading-steps {
    margin-top: var(--spacing-lg);
    
    .loading-step {
      padding: var(--spacing-xs) 0;
      
      &.active {
        color: var(--warning-color);
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive Design
@media (max-width: 768px) {
  .game-app {
    .game-timer {
      top: var(--spacing-sm);
      right: var(--spacing-sm);
      font-size: var(--font-size-xs);
    }
  }
  
  .welcome-screen {
    .features-section {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  .navigation-buttons {
    flex-direction: column;
    
    button {
      width: 100%;
    }
  }
  
  .results-screen {
    .action-buttons {
      flex-direction: column;
    }
    
    .stats-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }
}

@media (max-width: 480px) {
  .welcome-screen {
    .features-section {
      grid-template-columns: 1fr;
    }
  }
  
  .image-upload-screen {
    .image-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  .results-screen {
    .gallery-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-grid {
      grid-template-columns: 1fr;
    }
  }
}

// Debug styles (development only)
.debug-info {
  position: fixed;
  bottom: var(--spacing-md);
  left: var(--spacing-md);
  background: rgba(0, 0, 0, 0.8);
  color: var(--white);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  max-width: 300px;
  z-index: 999;
  
  details {
    summary {
      cursor: pointer;
      margin-bottom: var(--spacing-xs);
    }
    
    pre {
      max-height: 200px;
      overflow: auto;
      font-size: 10px;
    }
  }
}
