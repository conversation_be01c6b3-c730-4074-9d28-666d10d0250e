import { openMiniApp } from "zmp-sdk";
import { Box, Button, Icon, Page, Text } from "zmp-ui";
import { useNavigate } from "react-router-dom";

import Clock from "@/components/clock";
import Logo from "@/components/logo";
import bg from "@/static/bg.svg";

function HomePage() {
  const navigate = useNavigate();

  return (
    <Page
      className="flex flex-col items-center justify-center space-y-6 bg-cover bg-center bg-no-repeat bg-white dark:bg-black"
      style={{
        backgroundImage: `url(${bg})`,
      }}
    >
      <Box></Box>
      <Box textAlign="center" className="space-y-1">
        <Text.Title size="xLarge">Interactive Game Experience</Text.Title>
        <Text>Upload images, answer surveys, and discover amazing results!</Text>
        <Clock />
      </Box>

      <Box className="space-y-4">
        <Button
          variant="primary"
          size="large"
          fullWidth
          onClick={() => navigate('/game/welcome')}
          className="game-start-button"
        >
          <Icon icon="zi-play" />
          Start Game
        </Button>

        <Button
          variant="secondary"
          suffixIcon={<Icon icon="zi-more-grid" />}
          onClick={() => {
            openMiniApp({
              appId: "1070750904448149704", // ZaUI Components
            });
          }}
        >
          ZaUI Component Library
        </Button>
      </Box>

      <Logo className="fixed bottom-8" />
    </Page>
  );
}

export default HomePage;
