// Game Utility Functions

export interface GameSession {
  sessionId: string;
  currentScreen: number;
  uploadedImages: File[];
  surveyResponses: SurveyResponse[];
  startTime: Date;
  completionTime?: Date;
}

export interface SurveyResponse {
  questionId: string;
  answer: string | string[] | number;
  timestamp: Date;
}

export interface Question {
  id: string;
  type: 'multiple-choice' | 'text' | 'rating' | 'yes-no' | 'checkbox' | 'slider';
  question: string;
  options?: string[];
  required: boolean;
  min?: number;
  max?: number;
}

export interface GameResults {
  score: number;
  category: string;
  insights: string[];
  videoUrl: string;
  shareableImage?: string;
  completionTime: number; // in seconds
}

// Session Management
export const generateSessionId = (): string => {
  return 'game_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
};

export const saveGameState = (state: GameSession): void => {
  try {
    localStorage.setItem('gameState', JSON.stringify({
      ...state,
      startTime: state.startTime.toISOString(),
      completionTime: state.completionTime?.toISOString()
    }));
  } catch (error) {
    console.error('Error saving game state:', error);
  }
};

export const loadGameState = (): GameSession | null => {
  try {
    const saved = localStorage.getItem('gameState');
    if (!saved) return null;
    
    const parsed = JSON.parse(saved);
    return {
      ...parsed,
      startTime: new Date(parsed.startTime),
      completionTime: parsed.completionTime ? new Date(parsed.completionTime) : undefined
    };
  } catch (error) {
    console.error('Error loading game state:', error);
    return null;
  }
};

export const clearGameState = (): void => {
  localStorage.removeItem('gameState');
};

// File Validation
export const SUPPORTED_IMAGE_FORMATS = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp'
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const MAX_FILES = 5;

export const validateImageFile = (file: File): string | null => {
  if (!SUPPORTED_IMAGE_FORMATS.includes(file.type)) {
    return 'Unsupported file format. Please use JPG, PNG, GIF, or WebP.';
  }
  
  if (file.size > MAX_FILE_SIZE) {
    return `File size too large. Maximum size is ${formatFileSize(MAX_FILE_SIZE)}.`;
  }
  
  return null;
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Survey Validation
export const validateSurveyAnswer = (question: Question, answer: string | string[] | number): boolean => {
  if (!question.required && (!answer || (Array.isArray(answer) && answer.length === 0))) {
    return true;
  }
  
  switch (question.type) {
    case 'text':
      return typeof answer === 'string' && answer.trim().length > 0;
    
    case 'multiple-choice':
    case 'yes-no':
      return typeof answer === 'string' && answer.length > 0;
    
    case 'checkbox':
      return Array.isArray(answer) && answer.length > 0;
    
    case 'rating':
    case 'slider':
      return typeof answer === 'number' && 
             answer >= (question.min || 1) && 
             answer <= (question.max || 10);
    
    default:
      return false;
  }
};

// Results Calculation
export const calculateGameResults = (
  images: File[], 
  responses: SurveyResponse[], 
  startTime: Date, 
  endTime: Date
): GameResults => {
  // Mock calculation - in real app, this would use AI/ML algorithms
  const baseScore = Math.floor(Math.random() * 50) + 30; // 30-80 base score
  const imageBonus = Math.min(images.length * 5, 20); // Up to 20 bonus points for images
  const completionBonus = responses.length === getSampleQuestions().length ? 10 : 0;
  
  const totalScore = Math.min(baseScore + imageBonus + completionBonus, 100);
  
  const categories = [
    'Creative Explorer',
    'Visual Storyteller',
    'Artistic Innovator', 
    'Digital Pioneer',
    'Aesthetic Curator'
  ];
  
  const category = categories[Math.floor(totalScore / 20)];
  
  const insights = generateInsights(images, responses, totalScore);
  
  const completionTime = Math.floor((endTime.getTime() - startTime.getTime()) / 1000);
  
  return {
    score: totalScore,
    category,
    insights,
    videoUrl: '/static/reward-video.mp4',
    shareableImage: '/static/shareable-result.png',
    completionTime
  };
};

const generateInsights = (
  images: File[], 
  responses: SurveyResponse[], 
  score: number
): string[] => {
  const allInsights = [
    'Your images show a strong sense of composition and color balance.',
    'You have a unique perspective that sets your work apart.',
    'Your survey responses indicate a creative and analytical mindset.',
    'You demonstrate excellent attention to visual details.',
    'Your style suggests you would excel in digital media creation.',
    'You show great potential for artistic growth and development.',
    'Your choices reflect a sophisticated understanding of aesthetics.',
    'You have a natural eye for capturing meaningful moments.',
    'Your responses suggest you value both creativity and technical skill.',
    'You demonstrate strong visual communication abilities.'
  ];
  
  // Select insights based on score and responses
  const numInsights = score > 80 ? 4 : score > 60 ? 3 : 2;
  return allInsights.sort(() => 0.5 - Math.random()).slice(0, numInsights);
};

// Sample Questions (in real app, these would come from API)
export const getSampleQuestions = (): Question[] => [
  {
    id: 'q1',
    type: 'multiple-choice',
    question: 'What is your primary interest in this game?',
    options: ['Entertainment', 'Learning', 'Social interaction', 'Competition'],
    required: true
  },
  {
    id: 'q2', 
    type: 'rating',
    question: 'How would you rate your photography skills?',
    min: 1,
    max: 5,
    required: true
  },
  {
    id: 'q3',
    type: 'checkbox',
    question: 'Which social media platforms do you use? (Select all that apply)',
    options: ['Facebook', 'Instagram', 'Twitter', 'TikTok', 'LinkedIn', 'YouTube'],
    required: false
  },
  {
    id: 'q4',
    type: 'text', 
    question: 'What do you hope to achieve from this experience?',
    required: true
  },
  {
    id: 'q5',
    type: 'yes-no',
    question: 'Would you recommend this game to your friends?',
    required: true
  },
  {
    id: 'q6',
    type: 'slider',
    question: 'How much time do you spend on mobile games daily? (hours)',
    min: 0,
    max: 8,
    required: true
  }
];

// Progress Tracking
export const getProgressPercentage = (currentScreen: number): number => {
  return (currentScreen / 4) * 100;
};

export const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

export const getTimeSpent = (startTime: Date, endTime?: Date): string => {
  const now = endTime || new Date();
  const diffMs = now.getTime() - startTime.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  return formatTime(diffSeconds);
};

// Sharing Functions
export const shareResults = async (results: GameResults): Promise<boolean> => {
  const shareData = {
    title: 'My Game Results',
    text: `I scored ${results.score} points and got "${results.category}"! Check out this amazing game!`,
    url: window.location.href
  };
  
  if (navigator.share) {
    try {
      await navigator.share(shareData);
      return true;
    } catch (error) {
      console.log('Error sharing:', error);
    }
  }
  
  // Fallback to clipboard
  try {
    await navigator.clipboard.writeText(shareData.text + ' ' + shareData.url);
    return true;
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    return false;
  }
};

// Image Processing
export const createImagePreview = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        resolve(e.target.result as string);
      } else {
        reject(new Error('Failed to read file'));
      }
    };
    reader.onerror = () => reject(new Error('File reading error'));
    reader.readAsDataURL(file);
  });
};

export const resizeImage = (file: File, maxWidth: number, maxHeight: number, quality: number = 0.8): Promise<File> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }
      
      canvas.width = width;
      canvas.height = height;
      
      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob((blob) => {
        if (blob) {
          const resizedFile = new File([blob], file.name, {
            type: file.type,
            lastModified: Date.now()
          });
          resolve(resizedFile);
        } else {
          resolve(file); // Return original if compression fails
        }
      }, file.type, quality);
    };
    
    img.src = URL.createObjectURL(file);
  });
};
